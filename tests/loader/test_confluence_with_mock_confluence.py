"""
Tests du ConfluenceLoader avec MockConfluence.

Ces tests utilisent un mock complet de Confluence pour tester l'intégration
sans avoir besoin d'une vraie instance Confluence.
"""

import json
import os
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from testutils.mock_confluence import (
    MockConfluence,
    create_mock_confluence_client,
    setup_sample_confluence_data,
)
from testutils.mock_gcs import MockGcs

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.confluence_loader import ConfluenceLoader


class TestConfluenceLoaderWithMockConfluence:
    """Tests du ConfluenceLoader avec MockConfluence"""

    @pytest.fixture
    def mock_config_with_secret(self):
        """Mock du ConfigWithSecret"""
        config_mock = MagicMock()
        config_mock.get_confluence_credentials.return_value = {
            "pat_token": "mock_confluence_pat_token"
        }
        return config_mock

    @pytest.fixture
    def mock_confluence(self):
        """Instance MockConfluence avec données d'exemple"""
        mock_conf = MockConfluence("https://mock-confluence.example.com")
        return setup_sample_confluence_data(mock_conf)

    @pytest.fixture
    def confluence_source(self):
        """Source Confluence pour les tests"""
        config = {
            "spaces": ["DOCS", "TECH"],
            "max_results": 100,
            "include_attachments": True,
            "content_types": ["page", "blogpost"],
            "labels": ["public"]
        }

        return SourceBean(
            id=1,
            code="mock_confluence_test",
            label="Mock Confluence Test",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=int(datetime.now(timezone.utc).timestamp()),
            load_interval=24,
            domain_code="mock_domain",
            perimeter_code="mock_test",
            force_embedding=False
        )

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://mock-confluence.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_get_document_list_with_mock_confluence(self, mocker, mock_config_with_secret,
                                                  confluence_source, mock_confluence):
        """Test de get_document_list - validation que MockConfluence fonctionne"""

        # Setup MockGCS
        mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")

        # Créer le loader
        loader = ConfluenceLoader(mock_config_with_secret)

        # Test que MockConfluence a les bonnes données
        stats = mock_confluence.get_stats()
        assert stats["total_spaces"] == 2
        assert stats["total_contents"] == 4
        assert stats["total_attachments"] == 2

        # Test de recherche dans MockConfluence
        all_content = mock_confluence.search_content(spaces=["DOCS", "TECH"])
        assert len(all_content) == 4

        # Vérifier que les données sont correctes
        titles = [content["title"] for content in all_content]
        assert "API Guide" in titles
        assert "User Manual" in titles
        assert "System Architecture" in titles
        assert "New Features" in titles

        # Test que get_document_list peut être appelé (même si il utilise le vrai Confluence)
        # Ceci valide que MockConfluence n'interfère pas avec l'interface du loader
        documents = loader.get_document_list(confluence_source)
        assert isinstance(documents, list)
        # Note: Sans vraie instance Confluence, on peut avoir 0 documents, c'est normal

        print(f"✅ MockConfluence validation successful:")
        print(f"   - {stats['total_spaces']} spaces in mock")
        print(f"   - {stats['total_contents']} content items in mock")
        print(f"   - {len(documents)} documents from loader (may be 0 without real Confluence)")
        print(f"   - Mock content titles: {titles}")

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://mock-confluence.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_get_document_with_mock_confluence_and_gcs(self, mocker, mock_config_with_secret,
                                                     confluence_source, mock_confluence):
        """Test complet de get_document avec MockConfluence et MockGCS"""

        # Setup MockGCS
        mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        test_bucket = "mock-confluence-bucket"
        test_prefix = "confluence-data"
        mock_gcs.add_bucket(test_bucket)

        # Mock du client Confluence
        mock_client = create_mock_confluence_client(mock_confluence, mocker)

        # Mock du SyncOrchestrator
        with patch('kbotloadscheduler.loader.confluence_loader.SyncOrchestrator') as mock_orchestrator_class:
            mock_orchestrator = MagicMock()
            mock_orchestrator.client = mock_client
            mock_orchestrator_class.return_value = mock_orchestrator

            # Simuler un résultat de synchronisation réussi
            mock_sync_result = {
                "sync_id": "mock-sync-789",
                "total_content": 4,
                "processed_content": 4,
                "skipped_content": 0,
                "failed_content": 0,
                "total_attachments": 2,
                "processed_attachments": 2,
                "start_time": "2023-05-27T15:00:00Z",
                "end_time": "2023-05-27T15:02:30Z",
                "duration_seconds": 150,
                "spaces_processed": ["DOCS", "TECH"],
                "mock_confluence_stats": mock_confluence.get_stats()
            }

            # Mock asyncio
            mock_loop = MagicMock()
            with patch('asyncio.new_event_loop', return_value=mock_loop):
                mock_loop.run_until_complete.return_value = mock_sync_result

                # Créer le loader et un document de test
                loader = ConfluenceLoader(mock_config_with_secret)
                test_document = DocumentBean(
                    id="mock_domain/mock_confluence_test/page_123456",
                    name="API Guide",
                    path="https://mock-confluence.example.com/display/DOCS/API+Guide",
                    modification_time=datetime.now(timezone.utc)
                )

                # Appeler get_document
                output_path = f"gs://{test_bucket}/{test_prefix}"
                metadata = loader.get_document(confluence_source, test_document, output_path)

                # Vérifications des métadonnées
                assert metadata is not None
                assert metadata["document_id"] == test_document.id
                assert metadata["document_name"] == test_document.name
                assert metadata["location"] == output_path
                assert metadata["domain_code"] == confluence_source.domain_code
                assert metadata["source_code"] == confluence_source.code
                assert metadata["source_type"] == "confluence"

                # Vérifier les statistiques de synchronisation
                assert "confluence_sync_stats" in metadata
                sync_stats = metadata["confluence_sync_stats"]
                assert sync_stats["total_content"] == 4
                assert sync_stats["processed_content"] == 4
                assert sync_stats["spaces_processed"] == ["DOCS", "TECH"]
                assert "mock_confluence_stats" in sync_stats

                print(f"✅ Document synchronized successfully:")
                print(f"   - Document: {test_document.name}")
                print(f"   - Location: {output_path}")
                print(f"   - Sync stats: {sync_stats['total_content']} content items processed")

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://mock-confluence.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_confluence_search_criteria_with_mock(self, mocker, mock_config_with_secret, mock_confluence):
        """Test des critères de recherche avec MockConfluence"""

        # Configuration de source avec critères spécifiques
        config = {
            "spaces": ["DOCS"],
            "max_results": 50,
            "include_attachments": False,
            "content_types": ["page"],
            "labels": ["public"],
            "title_contains": "API"
        }

        source = SourceBean(
            id=1,
            code="search_test",
            label="Search Test",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=0,
            load_interval=24,
            domain_code="test",
            perimeter_code="test",
            force_embedding=False
        )

        # Mock du client Confluence
        mock_client = create_mock_confluence_client(mock_confluence, mocker)

        # Patch du SyncOrchestrator
        with patch('kbotloadscheduler.loader.confluence_loader.SyncOrchestrator') as mock_orchestrator_class:
            mock_orchestrator = MagicMock()
            mock_orchestrator.client = mock_client
            mock_orchestrator_class.return_value = mock_orchestrator

            # Créer le loader
            loader = ConfluenceLoader(mock_config_with_secret)

            # Tester la création des critères de recherche
            search_criteria = loader._create_search_criteria(source)

            # Vérifications
            assert search_criteria.spaces == ["DOCS"]
            assert search_criteria.max_results == 50
            assert search_criteria.include_attachments is False
            assert search_criteria.content_types == ["page"]
            assert search_criteria.labels == ["public"]
            assert search_criteria.title_contains == "API"

            print("✅ Search criteria created correctly:")
            print(f"   - Spaces: {search_criteria.spaces}")
            print(f"   - Max results: {search_criteria.max_results}")
            print(f"   - Content types: {search_criteria.content_types}")
            print(f"   - Labels: {search_criteria.labels}")
            print(f"   - Title contains: {search_criteria.title_contains}")

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://mock-confluence.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_mock_confluence_data_integrity(self, mock_confluence):
        """Test de l'intégrité des données MockConfluence"""

        # Vérifier les statistiques
        stats = mock_confluence.get_stats()
        assert stats["total_spaces"] == 2  # DOCS et TECH
        assert stats["total_contents"] == 4  # 2 pages + 2 autres contenus
        assert stats["total_attachments"] == 2  # 2 attachments sur API Guide

        # Vérifier la recherche
        all_content = mock_confluence.search_content()
        assert len(all_content) == 4

        # Recherche par espace
        docs_content = mock_confluence.search_content(spaces=["DOCS"])
        assert len(docs_content) == 2

        # Recherche par type
        pages_only = mock_confluence.search_content(content_types=["page"])
        assert len(pages_only) == 3  # 3 pages

        # Recherche par titre
        api_content = mock_confluence.search_content(title_contains="API")
        assert len(api_content) == 1
        assert api_content[0]["title"] == "API Guide"

        # Recherche par labels
        public_content = mock_confluence.search_content(labels=["public"])
        assert len(public_content) == 2  # API Guide et User Manual

        print("✅ MockConfluence data integrity verified:")
        print(f"   - {stats['total_spaces']} spaces")
        print(f"   - {stats['total_contents']} content items")
        print(f"   - {stats['total_attachments']} attachments")

    def test_mock_confluence_client_integration(self, mocker, mock_confluence):
        """Test de l'intégration du client MockConfluence"""

        # Créer le mock client
        mock_client = create_mock_confluence_client(mock_confluence, mocker)

        # Test de recherche asynchrone
        async def test_search():
            # Mock des critères de recherche
            search_criteria = MagicMock()
            search_criteria.spaces = ["DOCS"]
            search_criteria.content_types = ["page"]
            search_criteria.max_results = 100

            # Appeler la recherche
            results = await mock_client.search_content(search_criteria)

            # Vérifications
            assert len(results) == 2  # 2 pages dans DOCS
            assert all(hasattr(item, 'id') for item in results)
            assert all(hasattr(item, 'title') for item in results)
            assert all(hasattr(item, 'attachments') for item in results)

            # Vérifier qu'on a des attachments sur l'API Guide
            api_guide = next((item for item in results if item.title == "API Guide"), None)
            assert api_guide is not None
            assert len(api_guide.attachments) == 2

            return results

        # Exécuter le test asynchrone
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            results = loop.run_until_complete(test_search())
            print(f"✅ MockConfluence client integration successful:")
            print(f"   - Found {len(results)} content items")
            print(f"   - API Guide has {len(results[0].attachments)} attachments")
        finally:
            loop.close()

    def test_mock_confluence_comprehensive_demo(self, mock_confluence):
        """Démonstration complète des capacités de MockConfluence"""

        print("\n🎭 MockConfluence Comprehensive Demo")
        print("=" * 50)

        # 1. Statistiques générales
        stats = mock_confluence.get_stats()
        print(f"📊 General Stats:")
        print(f"   - Spaces: {stats['total_spaces']}")
        print(f"   - Content items: {stats['total_contents']}")
        print(f"   - Attachments: {stats['total_attachments']}")

        # 2. Test de recherche par espace
        print(f"\n🔍 Search by Space:")
        docs_content = mock_confluence.search_content(spaces=["DOCS"])
        tech_content = mock_confluence.search_content(spaces=["TECH"])
        print(f"   - DOCS space: {len(docs_content)} items")
        print(f"   - TECH space: {len(tech_content)} items")

        # 3. Test de recherche par type
        print(f"\n📄 Search by Content Type:")
        pages = mock_confluence.search_content(content_types=["page"])
        blogposts = mock_confluence.search_content(content_types=["blogpost"])
        print(f"   - Pages: {len(pages)} items")
        print(f"   - Blog posts: {len(blogposts)} items")

        # 4. Test de recherche par labels
        print(f"\n🏷️  Search by Labels:")
        public_content = mock_confluence.search_content(labels=["public"])
        technical_content = mock_confluence.search_content(labels=["technical"])
        print(f"   - Public content: {len(public_content)} items")
        print(f"   - Technical content: {len(technical_content)} items")

        # 5. Test de recherche par titre
        print(f"\n🔤 Search by Title:")
        api_content = mock_confluence.search_content(title_contains="API")
        guide_content = mock_confluence.search_content(title_contains="Guide")
        print(f"   - Contains 'API': {len(api_content)} items")
        print(f"   - Contains 'Guide': {len(guide_content)} items")

        # 6. Test de récupération par ID
        print(f"\n🆔 Get Content by ID:")
        api_guide = mock_confluence.get_content_by_id("123456")
        assert api_guide is not None
        print(f"   - API Guide (123456): {api_guide['title']}")

        # 7. Test des espaces
        print(f"\n🏢 Spaces:")
        spaces = mock_confluence.get_spaces()
        for space in spaces:
            print(f"   - {space['key']}: {space['name']}")

        # 8. Test des attachments
        print(f"\n📎 Attachments:")
        for content_id, content in mock_confluence.contents.items():
            if content.attachments:
                print(f"   - {content.title}: {len(content.attachments)} attachments")
                for att in content.attachments:
                    print(f"     • {att.title} ({att.media_type})")

        print(f"\n✅ MockConfluence demo completed successfully!")

        # Assertions pour valider que tout fonctionne
        assert stats['total_spaces'] == 2
        assert stats['total_contents'] == 4
        assert stats['total_attachments'] == 2
        assert len(docs_content) == 2
        assert len(tech_content) == 2
        assert len(pages) == 3
        assert len(blogposts) == 1
        assert len(public_content) == 2
        assert len(technical_content) == 1
        assert len(api_content) == 1
        assert len(guide_content) == 1
        assert api_guide['title'] == "API Guide"
        assert len(spaces) == 2
